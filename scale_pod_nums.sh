#!/bin/bash

# @Title:		scale_pod_nums.py
# @Date:		2024-02-21 10:54:27
# @Author:		wanglh

# 提示用户输入命名空间  
read -p "Enter the namespace: " NAMESPACE  
  
# 检查命名空间是否为空  
if [ -z "$NAMESPACE" ]; then  
    echo "Namespace cannot be empty."  
    exit 1  
fi  
  
# 提示用户输入部署名称  
read -p "Enter the deployment name: " DEPLOYMENT  
  
# 检查部署名称是否为空  
if [ -z "$DEPLOYMENT" ]; then  
    echo "Deployment name cannot be empty."  
    exit 1  
fi  
  
# 提示用户输入Pod的数量  
read -p "Enter the number of replicas for the deployment: " REPLICAS  
  
# 检查输入是否为数字  
if ! [[ "$REPLICAS" =~ ^[0-9]+$ ]]; then  
    echo "Number of replicas must be a positive integer."  
    exit 1  
fi  
  
# 使用kubectl scale命令调整部署的Pod数量  
kubectl scale --namespace="$NAMESPACE" deployment/"$DEPLOYMENT" --replicas="$REPLICAS"  

# 输出调整后的信息 
echo "Scaled deployment '$DEPLOYMENT' in namespace '$NAMESPACE' to $REPLICAS replicas."
