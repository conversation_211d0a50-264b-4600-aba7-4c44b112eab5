#!/bin/bash

echo "=== Conda 修复脚本 ==="

# 1. 备份当前配置
echo "1. 备份当前 .zshrc..."
cp ~/.zshrc ~/.zshrc.backup.$(date +%Y%m%d_%H%M%S)

# 2. 清理 conda 配置
echo "2. 清理 conda 配置..."
conda clean --all -y 2>/dev/null || echo "conda clean 失败，继续..."

# 3. 重新初始化 conda
echo "3. 重新初始化 conda..."
/opt/anaconda3/bin/conda init zsh --no-plugins

# 4. 创建临时的 conda 别名
echo "4. 创建临时修复..."
echo 'alias conda="/opt/anaconda3/bin/conda --no-plugins"' >> ~/.zshrc

# 5. 测试 conda
echo "5. 测试 conda..."
source ~/.zshrc
/opt/anaconda3/bin/conda --no-plugins --version

echo "=== 修复完成 ==="
echo "请重新打开终端或运行 'source ~/.zshrc' 来应用更改"
echo "如果问题仍然存在，请使用: conda --no-plugins <command>"
