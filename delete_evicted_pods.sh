#!/bin/bash

# @Title:		delete_evicted_pods.py
# @Date:		2024-02-21 11:16:35
# @Author:		wanglh



# 函数：将字符串转换为小写  
# 参数：str - 要转换的字符串  
# 返回值：转换后的小写字符串  
if [[ $BASH_VERSION =~ ^[4-9]\. ]]; then  
    # 如果bash版本大于或等于4.0，定义使用参数扩展的函数  
    lowercase_string() {  
        local str="$1"  
        echo "${str,,}"  
    }  
else  
    # 如果bash版本低于4.0，定义使用tr命令的函数  
    lowercase_string() {  
        local str="$1"  
        echo "$str" | tr '[:upper:]' '[:lower:]'  
    }  
fi 


# 函数：获取并销毁状态为Evicted的Pod  
evicted_pods_destroy() {  
    local namespace="$1"  
    local deployment="$2"  
    # 使用kubectl获取所有pods，并使用grep和awk过滤出状态为Evicted且属于指定deployment的pods  
    local evicted_pods=($(kubectl get pods -n "$namespace" -o wide |  
        grep "Evicted" |  # 过滤出状态为Evicted的行  
        awk '{print $1}' |  # 提取第一列，即Pod名称  
        grep -w "$deployment"  # 过滤出名称中包含deployment名称的Pod  
    ))  

    if [ ${#evicted_pods[@]} -eq 0 ]; then  
        echo "No evicted pods found for deployment '$deployment' in namespace '$namespace'."  
        return  
    fi  
  
    # 显示将要销毁的Pod列表  
    echo "The following evicted pods will be destroyed:"  
    for pod in "${evicted_pods[@]}"; do  
        echo "- $pod"  
    done  
  
    # 确认是否销毁  
    read -p "Are you sure you want to destroy these evicted pods? (y/N) " confirm  
    confirm=$(lowercase_string "$confirm")  # 调用函数将字符串转换为小写   
  
    if [ "$confirm" != "y" ]; then  
        echo "Aborted. No pods were destroyed."  
        return  
    fi  
  
    # 销毁Pod  
    for pod in "${evicted_pods[@]}"; do  
        kubectl delete pod -n "$namespace" "$pod" --ignore-not-found=true  
        echo "Destroyed pod: $pod"  
    done  
}  
  
# 主逻辑  
read -p "Enter the namespace: " namespace  
read -p "Enter the deployment name: " deployment  
  
# 调用函数  
evicted_pods_destroy "$namespace" "$deployment"
