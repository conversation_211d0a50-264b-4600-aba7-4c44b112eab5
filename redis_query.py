import redis

def query_redis_databases(host='localhost', port=6379, password=None):
    # 要查询的键和字段
    hash_key = "com.lookbook.server.manager.cache.bean.AccessToken"
    field = "appid=wxeb1f2b4b554a72f1"
    
    # 遍历db0到db16
    for db in range(0, 17):
        try:
            # 连接到指定数据库
            r = redis.Redis(
                host=host,
                port=port,
                password=password,
                db=db,
                decode_responses=True  # 自动解码为字符串
            )
            
            # 检查键是否存在
            if r.exists(hash_key):
                # 检查字段是否存在
                if r.hexists(hash_key, field):
                    # 获取字段值
                    value = r.hget(hash_key, field)
                    print(f"DB {db} 中找到数据: {value}")
                else:
                    print(f"DB {db} 存在键，但不存在该字段")
            else:
                print(f"DB {db} 中未找到指定键")
                
        except Exception as e:
            print(f"查询DB {db} 时出错: {str(e)}")

if __name__ == "__main__":
    # 请根据实际情况修改Redis连接参数
    query_redis_databases(
        host='***********',  # Redis服务器地址
        port=6379,         # Redis端口
        password="cM66LLuFb70X"      # 如果有密码，请填写，如: password='your_password'
    )
