#!/bin/bash

# if [[ $BASH_VERSION =~ ^[4-9]\. ]]; then  
#     # 如果bash版本大于或等于4.0，定义使用参数扩展的函数  
#     lowercase_string() {  
#         local str="$1"  
#         echo "${str,,}"  
#     }  
# else  
#     # 如果bash版本低于4.0，定义使用tr命令的函数  
#     lowercase_string() {  
#         local str="$1"  
#         echo "$str" | tr '[:upper:]' '[:lower:]'  
#     }  
# fi


# DONENUM=500
# for i in $(seq 0 $DONENUM)
# do
#   if [ $(($i % 7)) == 0 ];then
#     echo $i
#   fi
# done


# str="how they are implemented and applied in computer"
# words=($(echo $str))
# for word in "${words[@]}"
# do
#     lenth=${#word}
#     if [[ $lenth -lt 8 ]];then
#     echo $word
#     fi
# done


# mems=($(cat nowcoder.txt | awk 'NR>1{print $4}'))
# res=0
# for mem in "${mems[@]}"
# do
#     res=$(( $res+$mem ))
# done
# echo $res


cat nowcoder.txt | awk 'NR>1{a+=$4}END{print a}'