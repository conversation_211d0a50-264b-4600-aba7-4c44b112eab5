#!/bin/bash  
# @Author:  wanglh
# @Date:    2024-02-20
# @Verison: V1.0
# @Desc:    restart deployment-name 重启deployment

# 提示用户输入部署名称  
read -p "Enter the deployment name: " DEPLOYMENT_NAME  
  
# 检查部署名称是否为空  
if [ -z "$DEPLOYMENT_NAME" ]; then  
    echo "Deployment name cannot be empty."  
    exit 1  
fi  
  
# 提示用户输入命名空间  
read -p "Enter the namespace (default is 'prod'): " NAMESPACE  
  
# 如果用户没有输入命名空间，则使用默认值'prod'  
if [ -z "$NAMESPACE" ]; then  
    NAMESPACE="prod"  
fi


echo "Waiting.... Restart Deployment: $DEPLOYMENT_NAME,  NAMESPACE: $NAMESPACE"

# 定义重启和检查状态的函数  
function restart_deployment {  
    kubectl -n "$NAMESPACE" rollout restart deployment "$DEPLOYMENT_NAME"  
    echo "Restarted deployment '$DEPLOYMENT_NAME' in namespace '$NAMESPACE'."  
}  
  
function check_rollout_status {  
    local status=$(kubectl -n "$NAMESPACE" rollout status deployment "$DEPLOYMENT_NAME" 2>&1)  
    if [[ $status == *"deployment \"$DEPLOYMENT_NAME\" successfully rolled out"* ]]; then  
        echo "Deployment '$DEPLOYMENT_NAME' has been successfully rolled out."  
        return 0  
    else  
        echo "Deployment '$DEPLOYMENT_NAME' rollout is not yet complete. Checking again..."  
        return 1  
    fi  
}  


MAX_WAIT_TIME=600  # 设置最大等待时间（秒），可以根据需要调整  
POLL_INTERVAL=20    # 设置轮询间隔（秒），可以根据需要调整  
WAIT_TIME=0        # 当前已等待时间  
  
# 执行重启操作  
restart_deployment  
  
# 循环检查部署状态直到成功或超时  
while [ $WAIT_TIME -lt $MAX_WAIT_TIME ]; do  
    if check_rollout_status; then  
        break  # 如果部署成功，跳出循环  
    fi  
  
    sleep $POLL_INTERVAL  # 等待一段时间再次检查  
    WAIT_TIME=$((WAIT_TIME + POLL_INTERVAL))  
done  
  
# 检查是否超时  
if [ $WAIT_TIME -ge $MAX_WAIT_TIME ]; then  
    echo "Deployment '$DEPLOYMENT_NAME' did not finish rolling out within $MAX_WAIT_TIME seconds."  
    exit 1  
fi  
